<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .icon-canvas {
            border: 1px solid #ccc;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #5a67d8;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 B端智能助手-APA 图标生成器</h1>
        <p>为Chrome扩展生成所需的图标文件</p>
        
        <div class="icon-preview" id="iconPreview">
            <!-- 图标将在这里生成 -->
        </div>
        
        <button onclick="generateAllIcons()" style="background: #4caf50; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px;">
            生成所有图标
        </button>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成所有图标"按钮</li>
                <li>分别点击每个图标下方的"下载"按钮</li>
                <li>将下载的文件重命名为对应的文件名：
                    <ul>
                        <li>icon-16.png → icon16.png</li>
                        <li>icon-32.png → icon32.png</li>
                        <li>icon-48.png → icon48.png</li>
                        <li>icon-128.png → icon128.png</li>
                    </ul>
                </li>
                <li>将重命名后的文件放入 <code>client/assets/icons/</code> 目录</li>
                <li>重新加载Chrome扩展</li>
            </ol>
        </div>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            canvas.className = 'icon-canvas';
            
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制机器人图标
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🤖', size/2, size/2);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function generateAllIcons() {
            const sizes = [16, 32, 48, 128];
            const container = document.getElementById('iconPreview');
            container.innerHTML = '';
            
            sizes.forEach(size => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const canvas = createIcon(size);
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadCanvas(canvas, `icon-${size}.png`);
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                label.style.fontWeight = 'bold';
                label.style.marginBottom = '10px';
                
                iconItem.appendChild(label);
                iconItem.appendChild(canvas);
                iconItem.appendChild(document.createElement('br'));
                iconItem.appendChild(downloadBtn);
                
                container.appendChild(iconItem);
            });
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateAllIcons();
        };
    </script>
</body>
</html>
