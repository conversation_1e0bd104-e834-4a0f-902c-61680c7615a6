# B端智能助手-APA 客户端插件

## 项目简介

B端智能助手-APA是一个Chrome浏览器插件，与服务端相结合，在服务端实现RPA，做操作流程的自动化任务编排。

## 功能特性

### 🎯 核心功能
- **气泡图标**: 在目标代理系统页面自动显示悬浮气泡，点击展开主界面
- **辅助任务**: 支持任务创建、Excel模板下载上传、任务列表查询
- **智能对话**: 与AI助手进行实时对话交流，支持快捷指令
- **系统设置**: 配置服务器地址、代理站点、通知设置等

### 🔧 技术特性
- **自动登录检测**: 智能检测目标系统登录状态
- **Cookie采集**: 自动采集并同步登录凭证到服务端
- **WebSocket通信**: 与服务端实时双向通信
- **多站点支持**: 支持配置多个目标代理系统

## 项目结构

```
client/
├── manifest.json           # Chrome扩展配置文件
├── background/             # 后台脚本
│   └── background.js      # 处理扩展生命周期和消息通信
├── content/               # 内容脚本
│   ├── content.js        # 主要业务逻辑和UI组件
│   └── content.css       # 样式文件
├── popup/                 # 弹出窗口
│   ├── popup.html        # 弹出窗口HTML
│   ├── popup.css         # 弹出窗口样式
│   └── popup.js          # 弹出窗口逻辑
├── assets/               # 静态资源
│   └── icons/           # 图标文件
└── README.md            # 项目说明
```

## 安装使用

### 开发环境安装

1. 打开Chrome浏览器，进入扩展管理页面 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 `client` 文件夹
5. 扩展安装完成

### 使用说明

1. **首次配置**
   - 点击扩展图标打开设置面板
   - 配置服务器地址（默认: ws://localhost:8080）
   - 设置目标代理站点信息

2. **日常使用**
   - 访问配置的目标代理系统网站
   - 右侧会自动显示悬浮气泡图标
   - 点击气泡展开主界面，开始使用各项功能

## 界面说明

### 气泡图标
- 默认显示在页面右侧中央
- 圆形机器人头像样式
- 悬停时有放大效果
- 点击切换主界面显示/隐藏

### 主界面
- **头部**: 显示"智能助手"标题、登录状态、连接状态
- **Tab导航**: 三个主要功能标签页
- **内容区域**: 根据选中标签显示对应功能

### 辅助任务Tab
- **发起新任务**: 选择任务类型、下载模板、上传Excel文件
- **任务列表**: 查看任务状态、搜索过滤、查看详情

### 智能对话Tab
- **对话界面**: 类似IM的聊天界面
- **快捷指令**: 预设常用指令按钮
- **消息输入**: 支持文本输入和回车发送

### 设置Tab
- **服务器配置**: 设置WebSocket服务器地址
- **代理站点**: 选择和配置目标系统
- **通知设置**: 桌面通知和声音提醒开关

## 技术实现

### 核心技术栈
- **Chrome Extension API**: 扩展基础功能
- **WebSocket**: 与服务端实时通信
- **JavaScript ES6+**: 现代JavaScript语法
- **CSS3**: 现代样式和动画效果

### 关键实现
- **内容脚本注入**: 在目标页面注入UI组件和业务逻辑
- **消息通信**: content script、background script、popup间的消息传递
- **状态管理**: 连接状态、登录状态的实时更新
- **存储管理**: 使用Chrome Storage API持久化配置

## 开发说明

### 调试方法
1. 在Chrome扩展管理页面点击"检查视图"
2. 使用开发者工具调试各个脚本
3. 查看Console输出和Network请求

### 常见问题
1. **气泡不显示**: 检查是否为配置的目标站点
2. **连接失败**: 确认服务端地址和端口正确
3. **功能异常**: 查看Console错误信息

## 版本信息

- **当前版本**: 1.0.0
- **兼容性**: Chrome 88+
- **更新日期**: 2024-01-15

## 联系支持

如有问题或建议，请联系开发团队。
