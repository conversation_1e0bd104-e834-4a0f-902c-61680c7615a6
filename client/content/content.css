/* 气泡图标样式 */
.apa-floating-bubble {
  position: fixed;
  top: 50%;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  cursor: pointer;
  z-index: 10000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%);
}

.apa-floating-bubble:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.apa-floating-bubble .robot-icon {
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

/* 主面板样式 */
.apa-main-panel {
  position: fixed;
  top: 50%;
  right: 90px;
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  transform: translateY(-50%);
  display: none;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.apa-main-panel.show {
  display: flex;
}

/* 头部样式 */
.apa-panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.apa-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.apa-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.apa-login-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.apa-login-status.logged-in {
  background: #dcfce7;
  color: #166534;
}

.apa-login-status.not-logged-in {
  background: #fef2f2;
  color: #dc2626;
}

.apa-connection-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.apa-connection-status.connected {
  background: #dcfce7;
  color: #166534;
}

.apa-connection-status.connecting {
  background: #fef3c7;
  color: #d97706;
}

.apa-connection-status.disconnected {
  background: #fef2f2;
  color: #dc2626;
}

/* Tab导航样式 */
.apa-tab-nav {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.apa-tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.apa-tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8fafc;
}

.apa-tab-button:hover {
  background: #f8fafc;
  color: #374151;
}

/* Tab内容样式 */
.apa-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.apa-tab-pane {
  display: none;
}

.apa-tab-pane.active {
  display: block;
}

/* 按钮样式 */
.apa-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-right: 8px;
  margin-bottom: 8px;
}

.apa-btn-primary {
  background: #667eea;
  color: white;
}

.apa-btn-primary:hover {
  background: #5a67d8;
}

.apa-btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.apa-btn-secondary:hover {
  background: #d1d5db;
}

/* 任务Tab样式 */
.apa-tasks-container {
  height: 100%;
}

.apa-task-actions {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

/* 聊天Tab样式 */
.apa-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.apa-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  margin-bottom: 16px;
}

.apa-chat-message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.apa-chat-message.apa-chat-user {
  flex-direction: row-reverse;
}

.apa-message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
  background: #f3f4f6;
}

.apa-chat-user .apa-message-avatar {
  background: #667eea;
  color: white;
}

.apa-message-content {
  flex: 1;
  max-width: 280px;
}

.apa-message-text {
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.4;
}

.apa-chat-user .apa-message-text {
  background: #667eea;
  color: white;
}

.apa-message-time {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 4px;
  padding: 0 16px;
}

.apa-chat-input-container {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.apa-quick-commands {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.apa-quick-cmd {
  padding: 6px 12px;
  background: #f3f4f6;
  border: none;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.apa-quick-cmd:hover {
  background: #e5e7eb;
  color: #374151;
}

.apa-input-row {
  display: flex;
  gap: 8px;
}

.apa-chat-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.apa-chat-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.apa-send-btn {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.apa-send-btn:hover {
  background: #5a67d8;
}

/* 设置Tab样式 */
.apa-settings-container {
  height: 100%;
}

.apa-setting-group {
  margin-bottom: 20px;
}

.apa-setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.apa-setting-input,
.apa-setting-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.apa-setting-input:focus,
.apa-setting-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.apa-setting-checkbox {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.apa-setting-checkbox input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.apa-save-settings {
  width: 100%;
  margin-top: 20px;
}

/* 表单样式 */
.apa-form-group {
  margin-bottom: 16px;
}

.apa-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.apa-form-select,
.apa-file-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.apa-form-select:focus,
.apa-file-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.apa-template-actions {
  margin-bottom: 12px;
}

.apa-file-info {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.apa-file-selected {
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  color: #0369a1;
}

.apa-file-size {
  color: #6b7280;
}

.apa-form-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 搜索表单样式 */
.apa-search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.apa-search-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.apa-search-input,
.apa-search-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

/* 表格样式 */
.apa-task-table {
  overflow-x: auto;
}

.apa-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.apa-table th,
.apa-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.apa-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.apa-table td {
  color: #6b7280;
}

.apa-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.apa-status-pending {
  background: #fef3c7;
  color: #d97706;
}

.apa-status-running {
  background: #dbeafe;
  color: #2563eb;
}

.apa-status-success {
  background: #dcfce7;
  color: #166534;
}

.apa-status-failed {
  background: #fef2f2;
  color: #dc2626;
}

.apa-btn-link {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.apa-btn-link:hover {
  color: #5a67d8;
}
