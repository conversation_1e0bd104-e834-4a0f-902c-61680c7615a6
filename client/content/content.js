// B端智能助手-APA 内容脚本
class APAAssistant {
  constructor() {
    this.isVisible = false;
    this.currentTab = 'tasks';
    this.connectionStatus = 'disconnected';
    this.loginStatus = 'not-logged-in';
    this.websocket = null;

    this.init();
  }

  init() {
    // 检查是否为目标代理系统
    if (this.isTargetSystem()) {
      this.createFloatingBubble();
      this.createMainPanel();
      this.checkLoginStatus();
      this.connectToServer();
    }

    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true;
    });
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'toggleAssistant':
        this.toggleMainPanel();
        sendResponse({ success: true });
        break;

      case 'switchTab':
        if (this.isVisible) {
          this.switchTab(request.tab);
        } else {
          this.toggleMainPanel();
          setTimeout(() => this.switchTab(request.tab), 100);
        }
        sendResponse({ success: true });
        break;

      case 'reconnectServer':
        this.connectToServer(request.serverAddress);
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  isTargetSystem() {
    // 这里应该根据配置的目标系统域名来判断
    // 暂时返回true，实际应该从存储中获取配置的代理站点列表
    return true;
  }

  createFloatingBubble() {
    const bubble = document.createElement('div');
    bubble.className = 'apa-floating-bubble';
    bubble.innerHTML = `
      <div class="robot-icon">🤖</div>
    `;

    bubble.addEventListener('click', () => {
      this.toggleMainPanel();
    });

    document.body.appendChild(bubble);
  }

  createMainPanel() {
    const panel = document.createElement('div');
    panel.className = 'apa-main-panel';
    panel.innerHTML = `
      <div class="apa-panel-header">
        <div class="apa-header-left">
          <span class="apa-title">智能助手</span>
          <span class="apa-login-status ${this.loginStatus === 'logged-in' ? 'logged-in' : 'not-logged-in'}"
                onclick="window.apaAssistant.handleLoginClick()">
            ${this.loginStatus === 'logged-in' ? '已登录' : '未获取到登录态'}
          </span>
        </div>
        <span class="apa-connection-status ${this.connectionStatus}" id="apa-connection-status">
          ${this.getConnectionStatusText()}
        </span>
      </div>

      <div class="apa-tab-nav">
        <button class="apa-tab-button active" data-tab="tasks">辅助任务</button>
        <button class="apa-tab-button" data-tab="chat">智能对话</button>
        <button class="apa-tab-button" data-tab="settings">设置</button>
      </div>

      <div class="apa-tab-content">
        <div class="apa-tab-pane active" id="apa-tasks-tab">
          ${this.createTasksTabContent()}
        </div>
        <div class="apa-tab-pane" id="apa-chat-tab">
          ${this.createChatTabContent()}
        </div>
        <div class="apa-tab-pane" id="apa-settings-tab">
          ${this.createSettingsTabContent()}
        </div>
      </div>
    `;

    // 添加tab切换事件
    panel.addEventListener('click', (e) => {
      if (e.target.classList.contains('apa-tab-button')) {
        this.switchTab(e.target.dataset.tab);
      }
    });

    document.body.appendChild(panel);
    window.apaAssistant = this; // 全局引用以便事件处理
  }

  createTasksTabContent() {
    return `
      <div class="apa-tasks-container">
        <div class="apa-task-actions">
          <button class="apa-btn apa-btn-primary" onclick="window.apaAssistant.showNewTaskForm()">
            发起新任务
          </button>
          <button class="apa-btn apa-btn-secondary" onclick="window.apaAssistant.showTaskList()">
            任务列表
          </button>
        </div>

        <div id="apa-task-content">
          <!-- 动态内容区域 -->
        </div>
      </div>
    `;
  }

  createChatTabContent() {
    return `
      <div class="apa-chat-container">
        <div class="apa-chat-messages" id="apa-chat-messages">
          <div class="apa-chat-message apa-chat-assistant">
            <div class="apa-message-avatar">🤖</div>
            <div class="apa-message-content">
              <div class="apa-message-text">您好！我是智能助手，有什么可以帮助您的吗？</div>
              <div class="apa-message-time">${new Date().toLocaleTimeString()}</div>
            </div>
          </div>
        </div>

        <div class="apa-chat-input-container">
          <div class="apa-quick-commands">
            <button class="apa-quick-cmd" onclick="window.apaAssistant.sendQuickCommand('查看任务状态')">
              查看任务状态
            </button>
            <button class="apa-quick-cmd" onclick="window.apaAssistant.sendQuickCommand('帮助')">
              帮助
            </button>
          </div>
          <div class="apa-input-row">
            <input type="text" class="apa-chat-input" placeholder="输入消息..."
                   onkeypress="window.apaAssistant.handleChatKeyPress(event)">
            <button class="apa-send-btn" onclick="window.apaAssistant.sendMessage()">发送</button>
          </div>
        </div>
      </div>
    `;
  }

  createSettingsTabContent() {
    return `
      <div class="apa-settings-container">
        <div class="apa-setting-group">
          <label class="apa-setting-label">服务器地址</label>
          <input type="text" class="apa-setting-input" id="server-address"
                 placeholder="ws://localhost:8080" value="ws://localhost:8080">
        </div>

        <div class="apa-setting-group">
          <label class="apa-setting-label">代理站点</label>
          <select class="apa-setting-select" id="proxy-site">
            <option value="">请选择代理站点</option>
            <option value="erp">积理ERP</option>
            <option value="wanshang">万商</option>
            <option value="haibo">海博</option>
          </select>
        </div>

        <div class="apa-setting-group">
          <label class="apa-setting-label">代理站点SSO</label>
          <input type="text" class="apa-setting-input" id="proxy-sso"
                 placeholder="SSO登录地址">
        </div>

        <div class="apa-setting-group">
          <label class="apa-setting-checkbox">
            <input type="checkbox" id="desktop-notification"> 桌面通知
          </label>
        </div>

        <div class="apa-setting-group">
          <label class="apa-setting-checkbox">
            <input type="checkbox" id="sound-notification"> 声音提醒
          </label>
        </div>

        <button class="apa-btn apa-btn-primary apa-save-settings"
                onclick="window.apaAssistant.saveSettings()">
          保存设置
        </button>
      </div>
    `;
  }

  toggleMainPanel() {
    const panel = document.querySelector('.apa-main-panel');
    this.isVisible = !this.isVisible;

    if (this.isVisible) {
      panel.classList.add('show');
    } else {
      panel.classList.remove('show');
    }
  }

  switchTab(tabName) {
    // 更新tab按钮状态
    document.querySelectorAll('.apa-tab-button').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 更新tab内容
    document.querySelectorAll('.apa-tab-pane').forEach(pane => {
      pane.classList.remove('active');
    });
    document.getElementById(`apa-${tabName}-tab`).classList.add('active');

    this.currentTab = tabName;
  }

  getConnectionStatusText() {
    const statusMap = {
      'connected': '已连接',
      'connecting': '连接中',
      'disconnected': '未连接',
      'retry': '重试'
    };
    return statusMap[this.connectionStatus] || '未知';
  }

  checkLoginStatus() {
    // 检查登录状态的逻辑
    // 这里应该检查特定的cookie来判断是否已登录
    chrome.runtime.sendMessage({
      action: 'checkLogin',
      domain: window.location.hostname
    }, (response) => {
      if (response && response.isLoggedIn) {
        this.updateLoginStatus('logged-in');
      }
    });
  }

  updateLoginStatus(status) {
    this.loginStatus = status;
    const statusElement = document.querySelector('.apa-login-status');
    if (statusElement) {
      statusElement.className = `apa-login-status ${status === 'logged-in' ? 'logged-in' : 'not-logged-in'}`;
      statusElement.textContent = status === 'logged-in' ? '已登录' : '未获取到登录态';
    }
  }

  handleLoginClick() {
    if (this.loginStatus === 'not-logged-in') {
      // 跳转到SSO登录页面
      chrome.runtime.sendMessage({
        action: 'redirectToSSO',
        domain: window.location.hostname
      });
    }
  }

  connectToServer(serverAddress = null) {
    // WebSocket连接逻辑
    this.updateConnectionStatus('connecting');

    // 如果没有传入地址，从设置中获取服务器地址
    if (!serverAddress) {
      serverAddress = 'ws://localhost:8080';
      // 这里应该从Chrome存储中获取配置的服务器地址
      chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
        if (response && response.settings && response.settings.serverAddress) {
          this.connectToServer(response.settings.serverAddress);
          return;
        }
      });
      return;
    }

    try {
      this.websocket = new WebSocket(serverAddress);

      this.websocket.onopen = () => {
        this.updateConnectionStatus('connected');
      };

      this.websocket.onclose = () => {
        this.updateConnectionStatus('disconnected');
        // 自动重连逻辑
        setTimeout(() => this.connectToServer(), 5000);
      };

      this.websocket.onerror = () => {
        this.updateConnectionStatus('retry');
      };

      this.websocket.onmessage = (event) => {
        this.handleServerMessage(JSON.parse(event.data));
      };
    } catch (error) {
      this.updateConnectionStatus('disconnected');
    }
  }

  updateConnectionStatus(status) {
    this.connectionStatus = status;
    const statusElement = document.getElementById('apa-connection-status');
    if (statusElement) {
      statusElement.className = `apa-connection-status ${status}`;
      statusElement.textContent = this.getConnectionStatusText();
    }
  }

  handleServerMessage(message) {
    // 处理服务端消息
    if (message.type === 'chat') {
      this.addChatMessage(message.content, 'assistant');
    }
  }

  // 任务相关方法
  showNewTaskForm() {
    const content = document.getElementById('apa-task-content');
    content.innerHTML = `
      <div class="apa-new-task-form">
        <h3 style="margin-bottom: 16px; color: #374151;">发起新任务</h3>

        <div class="apa-form-group">
          <label class="apa-form-label">任务类型</label>
          <select class="apa-form-select" id="task-type" onchange="window.apaAssistant.onTaskTypeChange()">
            <option value="">请选择任务类型</option>
            <option value="data-import">数据导入</option>
            <option value="report-generate">报表生成</option>
            <option value="batch-process">批量处理</option>
          </select>
        </div>

        <div class="apa-form-group" id="template-section" style="display: none;">
          <label class="apa-form-label">Excel模板</label>
          <div class="apa-template-actions">
            <button class="apa-btn apa-btn-secondary" onclick="window.apaAssistant.downloadTemplate()">
              下载模板
            </button>
          </div>
          <input type="file" class="apa-file-input" id="excel-file" accept=".xlsx,.xls"
                 onchange="window.apaAssistant.onFileSelect()">
          <div class="apa-file-info" id="file-info"></div>
        </div>

        <div class="apa-form-actions">
          <button class="apa-btn apa-btn-primary" onclick="window.apaAssistant.submitTask()"
                  id="submit-task-btn" disabled>
            提交任务
          </button>
          <button class="apa-btn apa-btn-secondary" onclick="window.apaAssistant.showTaskList()">
            返回列表
          </button>
        </div>
      </div>
    `;
  }

  showTaskList() {
    const content = document.getElementById('apa-task-content');
    content.innerHTML = `
      <div class="apa-task-list">
        <h3 style="margin-bottom: 16px; color: #374151;">任务列表</h3>

        <div class="apa-search-form">
          <div class="apa-search-row">
            <input type="text" class="apa-search-input" placeholder="任务名称" id="search-name">
            <input type="date" class="apa-search-input" id="search-date">
            <select class="apa-search-select" id="search-status">
              <option value="">全部状态</option>
              <option value="pending">待执行</option>
              <option value="running">执行中</option>
              <option value="success">成功</option>
              <option value="failed">失败</option>
            </select>
            <button class="apa-btn apa-btn-primary" onclick="window.apaAssistant.searchTasks()">
              查询
            </button>
          </div>
        </div>

        <div class="apa-task-table">
          <table class="apa-table">
            <thead>
              <tr>
                <th>任务编号</th>
                <th>任务名称</th>
                <th>任务状态</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="task-list-body">
              <!-- 动态加载任务列表 -->
            </tbody>
          </table>
        </div>
      </div>
    `;

    this.loadTaskList();
  }

  onTaskTypeChange() {
    const taskType = document.getElementById('task-type').value;
    const templateSection = document.getElementById('template-section');

    if (taskType) {
      templateSection.style.display = 'block';
    } else {
      templateSection.style.display = 'none';
    }
  }

  downloadTemplate() {
    const taskType = document.getElementById('task-type').value;
    // 这里应该根据任务类型下载对应的Excel模板
    alert(`下载${taskType}模板`);
  }

  onFileSelect() {
    const fileInput = document.getElementById('excel-file');
    const fileInfo = document.getElementById('file-info');
    const submitBtn = document.getElementById('submit-task-btn');

    if (fileInput.files.length > 0) {
      const file = fileInput.files[0];
      fileInfo.innerHTML = `
        <div class="apa-file-selected">
          <span>已选择文件: ${file.name}</span>
          <span class="apa-file-size">(${(file.size / 1024).toFixed(1)} KB)</span>
        </div>
      `;
      submitBtn.disabled = false;
    } else {
      fileInfo.innerHTML = '';
      submitBtn.disabled = true;
    }
  }

  submitTask() {
    const taskType = document.getElementById('task-type').value;
    const fileInput = document.getElementById('excel-file');

    if (!taskType || !fileInput.files.length) {
      alert('请选择任务类型并上传Excel文件');
      return;
    }

    // 这里应该将文件上传到服务端
    alert('任务提交成功！');
    this.showTaskList();
  }

  loadTaskList() {
    // 模拟任务数据
    const tasks = [
      {
        id: 'T001',
        name: '数据导入任务',
        status: 'success',
        createTime: '2024-01-15 10:30:00'
      },
      {
        id: 'T002',
        name: '报表生成任务',
        status: 'running',
        createTime: '2024-01-15 11:00:00'
      }
    ];

    const tbody = document.getElementById('task-list-body');
    tbody.innerHTML = tasks.map(task => `
      <tr>
        <td>${task.id}</td>
        <td>${task.name}</td>
        <td><span class="apa-status apa-status-${task.status}">${this.getStatusText(task.status)}</span></td>
        <td>${task.createTime}</td>
        <td>
          <button class="apa-btn-link" onclick="window.apaAssistant.viewTaskDetail('${task.id}')">
            查看详情
          </button>
        </td>
      </tr>
    `).join('');
  }

  getStatusText(status) {
    const statusMap = {
      'pending': '待执行',
      'running': '执行中',
      'success': '成功',
      'failed': '失败'
    };
    return statusMap[status] || status;
  }

  searchTasks() {
    // 搜索任务逻辑
    this.loadTaskList();
  }

  viewTaskDetail(taskId) {
    alert(`查看任务 ${taskId} 的详情`);
  }

  // 聊天相关方法
  sendMessage() {
    const input = document.querySelector('.apa-chat-input');
    const message = input.value.trim();

    if (message) {
      this.addChatMessage(message, 'user');
      input.value = '';

      // 发送到服务端
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({
          type: 'chat',
          content: message
        }));
      }

      // 模拟机器人回复
      setTimeout(() => {
        this.addChatMessage('收到您的消息，正在处理中...', 'assistant');
      }, 1000);
    }
  }

  sendQuickCommand(command) {
    this.addChatMessage(command, 'user');

    // 根据快捷指令处理
    setTimeout(() => {
      let response = '';
      if (command === '查看任务状态') {
        response = '当前有2个任务正在执行中，1个任务已完成。';
      } else if (command === '帮助') {
        response = '我可以帮助您：\n1. 创建和管理任务\n2. 查看任务状态\n3. 系统设置配置';
      }
      this.addChatMessage(response, 'assistant');
    }, 500);
  }

  addChatMessage(content, sender) {
    const messagesContainer = document.getElementById('apa-chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `apa-chat-message apa-chat-${sender}`;

    const avatar = sender === 'user' ? '👤' : '🤖';
    const time = new Date().toLocaleTimeString();

    messageDiv.innerHTML = `
      <div class="apa-message-avatar">${avatar}</div>
      <div class="apa-message-content">
        <div class="apa-message-text">${content}</div>
        <div class="apa-message-time">${time}</div>
      </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  handleChatKeyPress(event) {
    if (event.key === 'Enter') {
      this.sendMessage();
    }
  }

  // 设置相关方法
  saveSettings() {
    const settings = {
      serverAddress: document.getElementById('server-address').value,
      proxySite: document.getElementById('proxy-site').value,
      proxySso: document.getElementById('proxy-sso').value,
      desktopNotification: document.getElementById('desktop-notification').checked,
      soundNotification: document.getElementById('sound-notification').checked
    };

    // 保存到Chrome存储
    chrome.runtime.sendMessage({
      action: 'saveSettings',
      settings: settings
    }, (response) => {
      if (response && response.success) {
        alert('设置保存成功！');
      }
    });
  }
}

// 初始化助手
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new APAAssistant();
  });
} else {
  new APAAssistant();
}
