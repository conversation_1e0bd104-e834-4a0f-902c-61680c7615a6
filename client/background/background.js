// B端智能助手-APA 后台脚本
class APABackground {
  constructor() {
    this.init();
  }

  init() {
    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听扩展安装
    chrome.runtime.onInstalled.addListener(() => {
      this.onInstalled();
    });

    // 监听tab更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.onTabUpdated(tabId, changeInfo, tab);
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'checkLogin':
          await this.checkLoginStatus(request, sendResponse);
          break;
        
        case 'redirectToSSO':
          await this.redirectToSSO(request, sendResponse);
          break;
        
        case 'saveSettings':
          await this.saveSettings(request, sendResponse);
          break;
        
        case 'getSettings':
          await this.getSettings(request, sendResponse);
          break;
        
        case 'collectCookies':
          await this.collectCookies(request, sendResponse);
          break;
        
        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Background script error:', error);
      sendResponse({ error: error.message });
    }
  }

  async checkLoginStatus(request, sendResponse) {
    try {
      const domain = request.domain;
      
      // 获取当前域名的cookies
      const cookies = await chrome.cookies.getAll({ domain: domain });
      
      // 检查登录相关的cookie
      // 这里应该根据不同的代理站点配置不同的登录cookie检查逻辑
      const loginCookies = cookies.filter(cookie => 
        cookie.name.includes('session') || 
        cookie.name.includes('token') || 
        cookie.name.includes('auth')
      );
      
      const isLoggedIn = loginCookies.length > 0;
      
      sendResponse({ isLoggedIn: isLoggedIn });
    } catch (error) {
      sendResponse({ isLoggedIn: false, error: error.message });
    }
  }

  async redirectToSSO(request, sendResponse) {
    try {
      const domain = request.domain;
      
      // 从设置中获取对应的SSO地址
      const settings = await this.getStoredSettings();
      let ssoUrl = '';
      
      // 根据域名匹配SSO地址
      if (settings.proxySites) {
        const siteConfig = settings.proxySites.find(site => 
          domain.includes(site.domain)
        );
        if (siteConfig) {
          ssoUrl = siteConfig.ssoUrl;
        }
      }
      
      if (!ssoUrl) {
        // 默认SSO地址或者从设置中获取
        ssoUrl = settings.proxySso || `https://${domain}/login`;
      }
      
      // 在新标签页中打开SSO登录页面
      chrome.tabs.create({ url: ssoUrl });
      
      sendResponse({ success: true });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async saveSettings(request, sendResponse) {
    try {
      const settings = request.settings;
      
      // 保存到Chrome存储
      await chrome.storage.sync.set({ apaSettings: settings });
      
      sendResponse({ success: true });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async getSettings(request, sendResponse) {
    try {
      const settings = await this.getStoredSettings();
      sendResponse({ settings: settings });
    } catch (error) {
      sendResponse({ settings: {}, error: error.message });
    }
  }

  async getStoredSettings() {
    const result = await chrome.storage.sync.get(['apaSettings']);
    return result.apaSettings || {
      serverAddress: 'ws://localhost:8080',
      proxySite: '',
      proxySso: '',
      desktopNotification: true,
      soundNotification: false,
      proxySites: [
        { domain: 'erp.example.com', name: '积理ERP', ssoUrl: 'https://erp.example.com/sso' },
        { domain: 'wanshang.example.com', name: '万商', ssoUrl: 'https://wanshang.example.com/login' },
        { domain: 'haibo.example.com', name: '海博', ssoUrl: 'https://haibo.example.com/auth' }
      ]
    };
  }

  async collectCookies(request, sendResponse) {
    try {
      const domain = request.domain;
      
      // 获取当前域名的所有cookies
      const cookies = await chrome.cookies.getAll({ domain: domain });
      
      // 过滤出重要的cookies（登录相关）
      const importantCookies = cookies.filter(cookie => 
        cookie.name.includes('session') || 
        cookie.name.includes('token') || 
        cookie.name.includes('auth') ||
        cookie.name.includes('user')
      );
      
      // 发送到服务端
      const settings = await this.getStoredSettings();
      if (settings.serverAddress) {
        this.sendCookiesToServer(settings.serverAddress, domain, importantCookies);
      }
      
      sendResponse({ success: true, cookiesCount: importantCookies.length });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async sendCookiesToServer(serverAddress, domain, cookies) {
    try {
      // 这里应该通过WebSocket或HTTP请求将cookies发送到服务端
      console.log('Sending cookies to server:', { domain, cookies });
      
      // 示例：通过fetch发送到服务端
      // const response = await fetch(`${serverAddress}/api/cookies`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ domain, cookies })
      // });
    } catch (error) {
      console.error('Failed to send cookies to server:', error);
    }
  }

  onInstalled() {
    console.log('APA Assistant installed');
    
    // 设置默认配置
    this.saveSettings({
      settings: {
        serverAddress: 'ws://localhost:8080',
        proxySite: '',
        proxySso: '',
        desktopNotification: true,
        soundNotification: false
      }
    }, () => {});
  }

  onTabUpdated(tabId, changeInfo, tab) {
    // 当页面加载完成时，自动收集cookies
    if (changeInfo.status === 'complete' && tab.url) {
      const url = new URL(tab.url);
      
      // 检查是否为目标代理系统
      this.isTargetSystem(url.hostname).then(isTarget => {
        if (isTarget) {
          // 自动收集cookies
          this.collectCookies({ domain: url.hostname }, () => {});
        }
      });
    }
  }

  async isTargetSystem(hostname) {
    const settings = await this.getStoredSettings();
    
    if (settings.proxySites) {
      return settings.proxySites.some(site => 
        hostname.includes(site.domain) || site.domain.includes(hostname)
      );
    }
    
    return false;
  }

  // 发送桌面通知
  showNotification(title, message, iconUrl = null) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconUrl || 'assets/icons/icon48.png',
      title: title,
      message: message
    });
  }
}

// 初始化后台脚本
new APABackground();
