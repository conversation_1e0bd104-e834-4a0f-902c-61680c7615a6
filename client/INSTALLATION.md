# B端智能助手-APA 安装和测试指南

## 🚀 快速开始

### 1. 安装Chrome扩展

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者通过菜单：更多工具 → 扩展程序

2. **启用开发者模式**
   - 在扩展管理页面右上角，开启"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择项目中的 `client` 文件夹
   - 扩展将自动安装并显示在扩展列表中

4. **验证安装**
   - 确认扩展图标出现在Chrome工具栏
   - 扩展状态显示为"已启用"

### 2. 准备图标文件（重要）

由于Chrome扩展需要图标文件，请先创建以下图标：

```bash
# 在 client/assets/icons/ 目录下需要以下文件：
icon16.png   # 16x16 像素
icon32.png   # 32x32 像素  
icon48.png   # 48x48 像素
icon128.png  # 128x128 像素
```

**临时解决方案：**
- 可以使用任意PNG图片重命名为对应文件名
- 或者使用在线工具生成机器人图标
- 推荐使用🤖表情符号转换为PNG图片

### 3. 测试扩展功能

#### 方法一：使用演示页面
1. 在浏览器中打开 `client/demo.html` 文件
2. 页面加载后，右侧应该出现悬浮的气泡图标
3. 点击气泡图标测试主界面功能

#### 方法二：在任意网站测试
1. 访问任意网站（由于当前配置为显示在所有网站）
2. 查看页面右侧是否出现气泡图标
3. 测试各项功能

## 🔧 功能测试清单

### ✅ 基础功能测试

- [ ] **气泡图标显示**
  - 页面右侧中央显示圆形气泡
  - 悬停时有放大效果
  - 点击可以展开/收起主界面

- [ ] **主界面显示**
  - 点击气泡后显示400x600的悬浮面板
  - 头部显示标题、登录状态、连接状态
  - 三个Tab标签正常显示

- [ ] **Tab切换功能**
  - 辅助任务、智能对话、设置三个Tab可正常切换
  - 切换时内容区域正确更新

### ✅ 辅助任务Tab测试

- [ ] **发起新任务**
  - 点击"发起新任务"按钮显示表单
  - 任务类型下拉菜单可选择
  - 选择任务类型后显示Excel模板区域
  - 文件上传功能正常

- [ ] **任务列表**
  - 点击"任务列表"显示任务表格
  - 搜索表单显示正常
  - 模拟任务数据正确显示

### ✅ 智能对话Tab测试

- [ ] **聊天界面**
  - 显示欢迎消息
  - 快捷指令按钮可点击
  - 消息输入框和发送按钮正常

- [ ] **消息发送**
  - 输入消息后点击发送
  - 消息正确显示在对话区域
  - 模拟机器人回复功能

### ✅ 设置Tab测试

- [ ] **设置表单**
  - 所有设置项正确显示
  - 服务器地址输入框有默认值
  - 代理站点下拉菜单有选项
  - 复选框可正常勾选

- [ ] **保存设置**
  - 点击"保存设置"按钮
  - 显示保存成功提示

### ✅ Popup界面测试

- [ ] **Popup显示**
  - 点击扩展图标打开popup
  - 界面布局正确显示
  - 状态信息正确显示

- [ ] **快速操作**
  - "显示/隐藏助手"按钮功能
  - "打开设置"按钮功能
  - "刷新状态"按钮功能

## 🐛 常见问题排查

### 问题1：气泡图标不显示
**可能原因：**
- 图标文件缺失
- 扩展未正确加载
- 页面CSP策略阻止

**解决方法：**
1. 检查 `client/assets/icons/` 目录下是否有图标文件
2. 在扩展管理页面重新加载扩展
3. 查看Console是否有错误信息

### 问题2：主界面样式异常
**可能原因：**
- CSS文件加载失败
- 样式冲突

**解决方法：**
1. 检查 `content.css` 文件是否存在
2. 查看开发者工具Network标签页
3. 检查是否有CSS加载错误

### 问题3：功能按钮无响应
**可能原因：**
- JavaScript错误
- 事件绑定失败

**解决方法：**
1. 打开开发者工具Console查看错误
2. 检查 `content.js` 文件语法
3. 确认事件处理函数正确绑定

### 问题4：与服务端连接失败
**可能原因：**
- 服务端未启动
- WebSocket地址错误
- 网络连接问题

**解决方法：**
1. 确认服务端正在运行
2. 检查WebSocket地址配置
3. 查看Network标签页的WebSocket连接状态

## 📝 开发调试

### 调试Content Script
1. 在目标页面按F12打开开发者工具
2. 在Console中可以访问 `window.apaAssistant` 对象
3. 查看Console输出的日志信息

### 调试Background Script
1. 在扩展管理页面点击扩展的"检查视图"
2. 选择"background page"
3. 在打开的开发者工具中调试

### 调试Popup
1. 右键点击扩展图标
2. 选择"检查弹出内容"
3. 在打开的开发者工具中调试

## 🎯 下一步开发

1. **添加真实图标文件**
2. **配置目标系统域名检测**
3. **实现与服务端的真实通信**
4. **完善错误处理和用户反馈**
5. **添加更多自动化任务类型**

## 📞 技术支持

如遇到问题，请：
1. 查看Console错误信息
2. 检查扩展是否正确安装
3. 确认所有文件完整性
4. 联系开发团队获取支持
