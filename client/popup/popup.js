// Popup脚本
class APAPopup {
  constructor() {
    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadSettings();
    await this.checkCurrentPageStatus();
    this.updateConnectionStatus();
  }

  bindEvents() {
    // 切换助手显示/隐藏
    document.getElementById('toggle-assistant').addEventListener('click', () => {
      this.toggleAssistant();
    });

    // 打开设置
    document.getElementById('open-settings').addEventListener('click', () => {
      this.openSettings();
    });

    // 刷新状态
    document.getElementById('refresh-status').addEventListener('click', () => {
      this.refreshStatus();
    });

    // 连接服务器
    document.getElementById('connect-server').addEventListener('click', () => {
      this.connectServer();
    });

    // 帮助链接
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.showHelp();
    });
  }

  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (response && response.settings) {
        const serverInput = document.getElementById('server-address');
        serverInput.value = response.settings.serverAddress || 'ws://localhost:8080';
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async checkCurrentPageStatus() {
    try {
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.url) {
        const url = new URL(tab.url);
        const hostname = url.hostname;
        
        // 检查是否为目标系统
        const isTarget = await this.isTargetSystem(hostname);
        this.updateTargetSystemStatus(isTarget ? '已识别' : '非目标系统');
        
        if (isTarget) {
          // 检查登录状态
          const loginResponse = await chrome.runtime.sendMessage({
            action: 'checkLogin',
            domain: hostname
          });
          
          if (loginResponse) {
            this.updateLoginStatus(loginResponse.isLoggedIn ? '已登录' : '未登录');
          }
        } else {
          this.updateLoginStatus('N/A');
        }
      }
    } catch (error) {
      console.error('Failed to check page status:', error);
      this.updateTargetSystemStatus('检测失败');
      this.updateLoginStatus('检测失败');
    }
  }

  async isTargetSystem(hostname) {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (response && response.settings && response.settings.proxySites) {
        return response.settings.proxySites.some(site => 
          hostname.includes(site.domain) || site.domain.includes(hostname)
        );
      }
    } catch (error) {
      console.error('Failed to check target system:', error);
    }
    return false;
  }

  updateTargetSystemStatus(status) {
    const element = document.getElementById('target-system');
    element.textContent = status;
    
    if (status === '已识别') {
      element.className = 'status-value success';
    } else if (status === '非目标系统') {
      element.className = 'status-value warning';
    } else {
      element.className = 'status-value error';
    }
  }

  updateLoginStatus(status) {
    const element = document.getElementById('login-status');
    element.textContent = status;
    
    if (status === '已登录') {
      element.className = 'status-value success';
    } else if (status === '未登录') {
      element.className = 'status-value warning';
    } else {
      element.className = 'status-value error';
    }
  }

  updateConnectionStatus(status = 'disconnected') {
    const statusElement = document.getElementById('connection-status');
    const dot = statusElement.querySelector('.status-dot');
    const text = statusElement.querySelector('.status-text');
    
    // 清除所有状态类
    dot.className = 'status-dot';
    
    switch (status) {
      case 'connected':
        dot.classList.add('connected');
        text.textContent = '已连接';
        break;
      case 'connecting':
        dot.classList.add('connecting');
        text.textContent = '连接中';
        break;
      default:
        text.textContent = '未连接';
    }
  }

  async toggleAssistant() {
    try {
      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab) {
        // 向content script发送切换消息
        await chrome.tabs.sendMessage(tab.id, { action: 'toggleAssistant' });
      }
    } catch (error) {
      console.error('Failed to toggle assistant:', error);
      alert('无法切换助手显示状态，请确保在支持的页面上操作。');
    }
  }

  openSettings() {
    // 获取当前活动标签页并切换到设置tab
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { 
          action: 'switchTab', 
          tab: 'settings' 
        });
        window.close(); // 关闭popup
      }
    });
  }

  async refreshStatus() {
    const refreshBtn = document.getElementById('refresh-status');
    const originalText = refreshBtn.querySelector('span:last-child').textContent;
    
    refreshBtn.querySelector('span:last-child').textContent = '刷新中...';
    refreshBtn.disabled = true;
    
    try {
      await this.checkCurrentPageStatus();
      this.updateConnectionStatus();
    } finally {
      refreshBtn.querySelector('span:last-child').textContent = originalText;
      refreshBtn.disabled = false;
    }
  }

  async connectServer() {
    const connectBtn = document.getElementById('connect-server');
    const serverInput = document.getElementById('server-address');
    const serverAddress = serverInput.value.trim();
    
    if (!serverAddress) {
      alert('请输入服务器地址');
      return;
    }
    
    connectBtn.textContent = '连接中...';
    connectBtn.disabled = true;
    this.updateConnectionStatus('connecting');
    
    try {
      // 保存服务器地址到设置
      await chrome.runtime.sendMessage({
        action: 'saveSettings',
        settings: { serverAddress: serverAddress }
      });
      
      // 模拟连接过程
      setTimeout(() => {
        this.updateConnectionStatus('connected');
        connectBtn.textContent = '连接';
        connectBtn.disabled = false;
        
        // 通知content script重新连接
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, { 
              action: 'reconnectServer',
              serverAddress: serverAddress
            });
          }
        });
      }, 2000);
      
    } catch (error) {
      console.error('Connection failed:', error);
      this.updateConnectionStatus('disconnected');
      connectBtn.textContent = '连接';
      connectBtn.disabled = false;
      alert('连接失败: ' + error.message);
    }
  }

  showHelp() {
    const helpContent = `
B端智能助手-APA 使用说明：

1. 气泡图标：在支持的目标系统页面会自动显示悬浮气泡
2. 点击气泡：展开/收起主界面
3. 辅助任务：创建和管理自动化任务
4. 智能对话：与AI助手进行对话交流
5. 设置：配置服务器地址和代理站点

如需更多帮助，请联系技术支持。
    `;
    
    alert(helpContent);
  }
}

// 初始化popup
document.addEventListener('DOMContentLoaded', () => {
  new APAPopup();
});
