/* Popup样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 350px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8fafc;
}

.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 头部样式 */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.popup-icon {
  font-size: 20px;
}

.popup-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

.status-dot.connected {
  background: #10b981;
}

.status-dot.connecting {
  background: #f59e0b;
}

/* 内容样式 */
.popup-content {
  flex: 1;
  padding: 20px;
}

.popup-section {
  margin-bottom: 24px;
}

.popup-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

/* 状态信息样式 */
.status-info {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.status-item:not(:last-child) {
  border-bottom: 1px solid #f3f4f6;
}

.status-label {
  font-size: 13px;
  color: #6b7280;
}

.status-value {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.status-value.success {
  color: #059669;
}

.status-value.error {
  color: #dc2626;
}

.status-value.warning {
  color: #d97706;
}

/* 快速操作样式 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f8fafc;
  border-color: #667eea;
  color: #667eea;
}

.btn-icon {
  font-size: 16px;
}

/* 服务器连接样式 */
.server-info {
  display: flex;
  gap: 8px;
}

.server-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  outline: none;
}

.server-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.connect-btn {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.connect-btn:hover {
  background: #5a67d8;
}

.connect-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 底部样式 */
.popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.footer-info a {
  color: #667eea;
  text-decoration: none;
}

.footer-info a:hover {
  text-decoration: underline;
}
