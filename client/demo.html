<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目标代理系统演示页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .header h1 {
            color: #667eea;
            margin: 0;
        }
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .section {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .section h3 {
            color: #374151;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .status-bar {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 20px;
            margin-top: 30px;
        }
        .instructions h4 {
            color: #d97706;
            margin-top: 0;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 积理ERP系统</h1>
            <p>企业资源规划管理系统演示页面</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot"></div>
                <span>系统状态: 正常运行</span>
            </div>
            <div class="status-item">
                <span>当前用户: 张三 (管理员)</span>
            </div>
            <div class="status-item">
                <span>最后登录: 2024-01-15 10:30:00</span>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h3>📊 数据管理</h3>
                <div class="form-group">
                    <label>数据类型</label>
                    <select>
                        <option>客户信息</option>
                        <option>订单数据</option>
                        <option>库存信息</option>
                        <option>财务数据</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>操作类型</label>
                    <select>
                        <option>数据导入</option>
                        <option>数据导出</option>
                        <option>数据同步</option>
                        <option>数据清理</option>
                    </select>
                </div>
                <button class="btn">执行操作</button>
            </div>

            <div class="section">
                <h3>📈 报表生成</h3>
                <div class="form-group">
                    <label>报表类型</label>
                    <select>
                        <option>销售报表</option>
                        <option>库存报表</option>
                        <option>财务报表</option>
                        <option>客户分析</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>时间范围</label>
                    <input type="date" value="2024-01-01">
                    <input type="date" value="2024-01-15" style="margin-top: 5px;">
                </div>
                <button class="btn">生成报表</button>
            </div>

            <div class="section">
                <h3>⚙️ 系统配置</h3>
                <div class="form-group">
                    <label>配置项</label>
                    <select>
                        <option>用户权限</option>
                        <option>系统参数</option>
                        <option>接口配置</option>
                        <option>安全设置</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>配置值</label>
                    <textarea rows="3" placeholder="请输入配置内容..."></textarea>
                </div>
                <button class="btn">保存配置</button>
            </div>

            <div class="section">
                <h3>🔄 批量处理</h3>
                <div class="form-group">
                    <label>处理任务</label>
                    <select>
                        <option>批量更新价格</option>
                        <option>批量发送邮件</option>
                        <option>批量生成单据</option>
                        <option>批量数据校验</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>上传文件</label>
                    <input type="file" accept=".xlsx,.xls,.csv">
                </div>
                <button class="btn">开始处理</button>
            </div>
        </div>

        <div class="instructions">
            <h4>🤖 智能助手使用说明</h4>
            <p>本页面已集成B端智能助手-APA，您可以：</p>
            <ul>
                <li>查看页面右侧的悬浮气泡图标（🤖）</li>
                <li>点击气泡图标展开智能助手界面</li>
                <li>使用"辅助任务"功能创建自动化任务</li>
                <li>通过"智能对话"与AI助手交流</li>
                <li>在"设置"中配置服务器和代理站点</li>
            </ul>
            <p><strong>注意：</strong>请确保已安装并启用B端智能助手-APA Chrome扩展。</p>
        </div>
    </div>

    <script>
        // 模拟一些页面交互
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.textContent;
                this.textContent = '处理中...';
                this.disabled = true;
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.disabled = false;
                    alert('操作完成！');
                }, 2000);
            });
        });

        // 设置一些模拟的登录cookies
        document.cookie = "session_id=abc123def456; path=/";
        document.cookie = "user_token=xyz789uvw012; path=/";
        document.cookie = "auth_status=logged_in; path=/";
    </script>
</body>
</html>
